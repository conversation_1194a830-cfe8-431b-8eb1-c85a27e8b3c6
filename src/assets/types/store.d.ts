namespace TsStore {
    interface Tab {
        label: string;
        name: string;
        icon?: string;
    }
    interface User {
        userId: number | "";
        avatar: string;
        userName: string;
        nickName: string;
        sex: string;
        phonenumber: number | "";
        admin: boolean;
        deptId: number | "";
        deptName: string;
        permissions: string[];
        roles: string[];
    }
    interface RouteDynamic {
        path: string;
        name: string;
        redirect?: string;
        viewPath: string;
        meta: {
            label: string;
            icon?: string;
            keepAlive?: boolean;
            noTab?: boolean;
        },
        children?: RouteDynamic[];
    }
    interface App {
        token: string;
        tabs: Tab[];
        tabsShowIcon: boolean;
        keepAlive: string[];
        collapse: boolean;
        theme: "light" | "dark";
        isLock: boolean;
        routesDynamic: RouteDynamic[];
        breadcrumbShowIcon: boolean;
    }
}