<script setup lang="ts">
defineProps<{
    modelValue: TsMenu.Model,
    options: TsMenu.Options,
    collapse?: TsMenu.Collapse,
    sets?: TsMenu.Sets,
}>()
const emits = defineEmits<{
    menuItem: [name: TsMenu.Model]
}>();
// 点击事件
function onMenuItem(name: TsMenu.Model) {
    router.push({name});
    emits("menuItem", name)
}
</script>
<template>
    <el-menu
        :default-active="modelValue"
        :collapse="collapse"
        :show-timeout="0"
        :hide-timeout="150"
        @select="onMenuItem"
        v-bind="sets"
        class="base-menu"
    >
        <base-menu-item v-for="item in options" :key="item.name" :option="item" :name="item.name"/>
    </el-menu>
</template>
<style scoped>
.base-menu {
    padding: var(--el-gap-half);
    border-right: none;
    min-width: 100%;
    width: 100%;
    max-width: 100%;
}

.base-menu :deep(li + li) {
    margin-top: var(--el-gap-half);
}
</style>
<style>
.el-menu {
	--el-menu-text-color: #e9e9eb;
    --el-menu-base-level-padding: 10px;
}
.el-popper.is-light:has(.el-menu--popup) {
    border: none;
}

.el-menu--popup {
    padding: 10px;
}
.el-menu--popup li + li {
    margin-top: var(--el-gap-half);
}
</style>