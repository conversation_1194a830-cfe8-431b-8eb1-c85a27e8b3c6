<script setup lang="ts">
const { breadcrumbShowIcon } = pinia.storeToRefs(storeApp());
const route = useRoute();
const options = computed(() => {
	let list: TsBreadcrumb.Options = [];
	if(route.name !== "Home") { 
		list = list.concat(route.matched.map(item => {
			return {
				label: item.meta?.label as string,
				name: item.name as string,
				icon: breadcrumbShowIcon.value ? item.meta?.icon as string : undefined,
			}
		}))
	}
	const routeHome = router.getRoutes().find(item => item.name === "Home");
	routeHome && list.unshift({
		label: routeHome?.meta?.label as string,
		name: routeHome?.name as string,
		icon: breadcrumbShowIcon.value ? routeHome?.meta?.icon as string : undefined,
	})
	return list;
});
</script>
<template>
    <div class="the-breadcrumb">
        <base-breadcrumb :options="options" />
    </div>
</template>
<style scoped>

</style>