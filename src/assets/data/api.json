{"swagger": "2.0", "info": {"title": "API文档", "version": "1.0.0", "description": "接口文档"}, "basePath": "/", "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "securityDefinitions": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "description": "JWT认证Token，格式为'Bearer {token}'"}}, "paths": {"/captchaImage": {"get": {"tags": ["登录"], "summary": "登录验证码", "description": "获取登录验证码", "produces": ["application/json"], "responses": {"200": {"description": "操作成功", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "example": 200, "description": "状态码"}, "msg": {"type": "string", "example": "操作成功", "description": "返回消息"}, "img": {"type": "string", "example": "base64字符串", "description": "验证码图片的base64编码"}, "uuid": {"type": "string", "example": "uuid", "description": "验证码唯一标识"}, "captchaEnabled": {"type": "boolean", "description": "验证码是否启用"}}}}}}}, "/login": {"post": {"tags": ["登录", "获取token"], "summary": "登录", "description": "用户登录接口", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "loginParams", "description": "登录参数", "required": true, "schema": {"type": "object", "properties": {"username": {"type": "string", "description": "账号"}, "password": {"type": "string", "description": "密码"}, "code": {"type": "string", "description": "验证码"}, "uuid": {"type": "string", "description": "验证码唯一标识"}}, "required": ["username", "password", "code", "uuid"]}}], "responses": {"200": {"description": "操作成功", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "example": 200, "description": "状态码"}, "msg": {"type": "string", "example": "操作成功", "description": "返回消息"}, "token": {"type": "string", "example": "sdfjklsjdflksjadlfkjasdlfjlsdjflksadj", "description": "认证token"}}}}}}}, "/logout": {"post": {"tags": ["退出登录"], "summary": "退出登录", "description": "用户退出登录接口", "produces": ["application/json"], "responses": {"200": {"description": "操作成功", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "example": 200, "description": "状态码"}, "msg": {"type": "string", "example": "退出成功", "description": "返回消息"}}}}}}}, "/getInfo": {"get": {"tags": ["基本信息"], "summary": "用户信息", "description": "获取当前登录用户信息", "produces": ["application/json"], "responses": {"200": {"description": "操作成功", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "example": 200, "description": "状态码"}, "msg": {"type": "string", "example": "操作成功", "description": "返回消息"}, "permissions": {"type": "array", "items": {"type": "string"}, "example": ["*:*:*"], "description": "权限列表"}, "roles": {"type": "array", "items": {"type": "string"}, "example": ["admin"], "description": "角色列表"}, "user": {"type": "object", "properties": {"userId": {"type": "integer", "example": 1, "description": "用户ID"}, "userName": {"type": "string", "example": "admin", "description": "用户名"}, "nickName": {"type": "string", "example": "昵称", "description": "用户昵称"}, "phonenumber": {"type": "integer", "format": "int64", "example": 13512348765, "description": "手机号码"}, "admin": {"type": "boolean", "description": "是否为管理员"}, "avatar": {"type": "string", "description": "头像地址"}, "sex": {"type": "string", "example": "1", "description": "性别"}, "dept": {"type": "object", "properties": {"deptId": {"type": "integer", "description": "部门ID"}, "deptName": {"type": "string", "description": "部门名称"}}}}}}}}}}}, "/system/user/list": {"get": {"tags": ["系统管理", "用户管理"], "summary": "用户列表", "description": "获取用户列表数据", "produces": ["application/json"], "parameters": [{"name": "pageNum", "in": "query", "description": "页码", "required": true, "type": "integer"}, {"name": "pageSize", "in": "query", "description": "每页条数", "required": true, "type": "integer"}, {"name": "userName", "in": "query", "description": "用户名称", "required": false, "type": "string"}, {"name": "phonenumber", "in": "query", "description": "手机号", "required": false, "type": "string"}, {"name": "status", "in": "query", "description": "状态", "required": false, "type": "string"}, {"name": "deptId", "in": "query", "description": "部门ID", "required": false, "type": "integer"}, {"name": "params[beginTime]", "in": "query", "description": "创建时间-开始", "required": false, "type": "string"}, {"name": "params[endTime]", "in": "query", "description": "创建时间-结束", "required": false, "type": "string"}], "responses": {"200": {"description": "操作成功", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "example": 200, "description": "状态码"}, "msg": {"type": "string", "example": "查询成功", "description": "返回消息"}, "total": {"type": "integer", "example": 10, "description": "总记录数"}, "rows": {"type": "array", "description": "用户列表数据", "items": {"type": "object", "properties": {"userId": {"type": "integer", "example": 1, "description": "用户ID"}, "createTime": {"type": "string", "example": "2024-09-24 14:49:04", "description": "创建时间"}, "remark": {"type": "string", "example": "管理员", "description": "备注"}, "userName": {"type": "string", "example": "admin", "description": "用户名"}, "nickName": {"type": "string", "example": "若依", "description": "昵称"}, "email": {"type": "string", "example": "<EMAIL>", "description": "邮箱"}, "phonenumber": {"type": "string", "example": "15888888888", "description": "手机号"}, "sex": {"type": "string", "example": "1", "description": "性别"}, "status": {"type": "string", "example": "0", "description": "状态"}, "dept": {"type": "object", "description": "部门信息", "properties": {"deptId": {"type": "integer", "example": 103, "description": "部门ID"}, "deptName": {"type": "string", "example": "研发部门", "description": "部门名称"}}}, "admin": {"type": "boolean", "example": true, "description": "是否为管理员"}}}}}}}}}}, "/system/user/": {"get": {"tags": ["用户管理"], "summary": "获取用户初始化信息", "description": "获取用户初始化信息，包括角色和岗位列表", "security": [{"Bearer": []}], "responses": {"200": {"description": "成功", "schema": {"type": "object", "properties": {"msg": {"type": "string", "example": "操作成功"}, "code": {"type": "integer", "example": 200}, "roles": {"type": "array", "items": {"$ref": "#/definitions/Role"}}, "posts": {"type": "array", "items": {"$ref": "#/definitions/Post"}}}}}, "401": {"description": "未授权"}, "500": {"description": "服务器内部错误"}}}}, "/system/user": {"post": {"tags": ["系统管理", "用户管理"], "summary": "新增用户", "description": "新增系统用户", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "userData", "description": "用户数据", "required": true, "schema": {"type": "object", "properties": {"nickName": {"type": "string", "description": "昵称", "example": "测试用户"}, "deptId": {"type": "integer", "description": "归属部门ID", "example": 103}, "userName": {"type": "string", "description": "账号", "example": "test123"}, "password": {"type": "string", "description": "密码", "example": "password123"}, "phonenumber": {"type": "string", "description": "手机号", "example": "13912345678"}, "email": {"type": "string", "description": "邮箱", "example": "<EMAIL>"}, "sex": {"type": "string", "description": "性别", "example": "0"}, "status": {"type": "string", "description": "状态", "example": "0"}, "postIds": {"type": "array", "description": "岗位ID数组", "items": {"type": "integer"}, "example": [1, 2]}, "roleIds": {"type": "array", "description": "角色ID数组", "items": {"type": "integer"}, "example": [2, 3]}, "remark": {"type": "string", "description": "备注", "example": "测试账号"}}, "required": ["nick<PERSON><PERSON>", "userName", "password", "status"]}}], "responses": {"200": {"description": "操作成功", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "example": 200, "description": "状态码"}, "msg": {"type": "string", "example": "操作成功", "description": "返回消息"}}}}}}, "put": {"tags": ["系统管理", "用户管理"], "summary": "修改用户", "description": "修改系统用户信息", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "userData", "description": "用户数据", "required": true, "schema": {"type": "object", "properties": {"userId": {"type": "integer", "description": "用户ID", "example": 111}, "nickName": {"type": "string", "description": "昵称", "example": "测试用户"}, "deptId": {"type": "integer", "description": "归属部门ID", "example": 103}, "userName": {"type": "string", "description": "用户名", "example": "test123"}, "phonenumber": {"type": "string", "description": "手机号", "example": "13912345678"}, "email": {"type": "string", "description": "邮箱", "example": "<EMAIL>"}, "sex": {"type": "string", "description": "性别", "example": "0"}, "status": {"type": "string", "description": "状态", "example": "0"}, "postIds": {"type": "array", "description": "岗位ID数组", "items": {"type": "integer"}, "example": [1, 2]}, "roleIds": {"type": "array", "description": "角色ID数组", "items": {"type": "integer"}, "example": [2, 3]}, "remark": {"type": "string", "description": "备注", "example": "测试账号"}}, "required": ["userId", "nick<PERSON><PERSON>", "deptId"]}}], "responses": {"200": {"description": "操作成功", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "example": 200, "description": "状态码"}, "msg": {"type": "string", "example": "操作成功", "description": "返回消息"}}}}}}}, "/system/user/{userId}": {"get": {"tags": ["系统管理", "用户管理"], "summary": "用户详情", "description": "获取指定用户的详细信息", "produces": ["application/json"], "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "type": "integer"}], "responses": {"200": {"description": "操作成功", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "example": 200, "description": "状态码"}, "msg": {"type": "string", "example": "操作成功", "description": "返回消息"}, "data": {"type": "object", "description": "用户详情数据", "properties": {"remark": {"type": "string", "example": "测试账号9", "description": "备注"}, "userId": {"type": "integer", "example": 111, "description": "用户ID"}, "deptId": {"type": "integer", "example": 105, "description": "部门ID"}, "userName": {"type": "string", "example": "test9", "description": "账号"}, "nickName": {"type": "string", "example": "测试昵称9", "description": "昵称"}, "email": {"type": "string", "example": "<EMAIL>", "description": "邮箱"}, "phonenumber": {"type": "string", "example": "13512348765", "description": "手机号"}, "sex": {"type": "string", "example": "0", "description": "性别"}, "avatar": {"type": "string", "example": "", "description": "头像"}, "password": {"type": "string", "example": "$2a$10$yCjdCny.mko9bZZ72zs6F.NB2KvbTY8xpFEiNxEr8ZTHNpihNcPBa", "description": "密码"}, "status": {"type": "string", "example": "0", "description": "状态"}, "dept": {"type": "object", "description": "部门信息", "properties": {"deptId": {"type": "integer", "example": 105, "description": "部门ID"}, "deptName": {"type": "string", "example": "测试部门", "description": "部门名称"}}}, "admin": {"type": "boolean", "example": false, "description": "是否为管理员"}}}, "postIds": {"type": "array", "description": "岗位ID数组", "items": {"type": "integer"}, "example": [3, 4]}, "posts": {"type": "array", "description": "可选岗位数据", "items": {"type": "object", "properties": {"postId": {"type": "integer", "example": 1, "description": "岗位ID"}, "postName": {"type": "string", "example": "董事长", "description": "岗位名称"}, "status": {"type": "string", "example": "0", "description": "岗位状态"}}}}, "roleIds": {"type": "array", "description": "角色ID数组", "items": {"type": "integer"}, "example": [101, 102]}, "roles": {"type": "array", "description": "可选角色数据", "items": {"type": "object", "properties": {"roleId": {"type": "integer", "example": 1, "description": "角色ID"}, "roleName": {"type": "string", "example": "普通角色", "description": "角色名称"}, "status": {"type": "string", "example": "0", "description": "角色状态"}}}}}}}}}, "delete": {"tags": ["系统管理", "用户管理"], "summary": "删除用户", "description": "删除指定用户", "produces": ["application/json"], "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "type": "integer"}], "responses": {"200": {"description": "操作成功", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "example": 200, "description": "状态码"}, "msg": {"type": "string", "example": "操作成功", "description": "返回消息"}}}}}}}, "/system/user/deptTree": {"get": {"tags": ["组织机构"], "summary": "组织机构树", "description": "获取组织机构树结构数据", "produces": ["application/json"], "parameters": [], "responses": {"200": {"description": "操作成功", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "example": 200, "description": "状态码"}, "msg": {"type": "string", "example": "查询成功", "description": "返回消息"}, "data": {"type": "array", "description": "组织机构树数据", "items": {"$ref": "#/definitions/DeptTreeNode"}}}}}}}}, "/system/user/resetPwd": {"put": {"tags": ["用户管理"], "summary": "用户密码重置", "description": "重置用户密码", "security": [{"Bearer": []}], "parameters": [{"name": "resetPwd", "in": "body", "description": "重置密码信息", "required": true, "schema": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int64", "description": "用户ID"}, "password": {"type": "string", "description": "新密码"}}, "required": ["userId", "password"]}}], "responses": {"200": {"description": "成功", "schema": {"type": "object", "properties": {"msg": {"type": "string", "example": "操作成功"}, "code": {"type": "integer", "example": 200}}}}, "401": {"description": "未授权"}, "500": {"description": "服务器内部错误"}}}}, "/system/user/changeStatus": {"put": {"tags": ["用户管理"], "summary": "用户状态修改", "description": "修改用户状态（启用/停用）", "security": [{"Bearer": []}], "parameters": [{"name": "statusInfo", "in": "body", "description": "状态信息", "required": true, "schema": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int64", "description": "用户ID"}, "status": {"type": "string", "description": "用户状态（0正常 1停用）"}}, "required": ["userId", "status"]}}], "responses": {"200": {"description": "成功", "schema": {"type": "object", "properties": {"msg": {"type": "string", "example": "操作成功"}, "code": {"type": "integer", "example": 200}}}}, "401": {"description": "未授权"}, "500": {"description": "服务器内部错误"}}}}, "/system/user/profile": {"get": {"tags": ["用户管理"], "summary": "查询用户个人信息", "description": "获取当前登录用户的个人信息", "security": [{"Bearer": []}], "responses": {"200": {"description": "成功", "schema": {"type": "object", "properties": {"msg": {"type": "string", "example": "操作成功"}, "code": {"type": "integer", "example": 200}, "data": {"$ref": "#/definitions/User"}, "roleGroup": {"type": "string", "description": "角色组"}, "postGroup": {"type": "string", "description": "岗位组"}}}}, "401": {"description": "未授权"}, "500": {"description": "服务器内部错误"}}}, "put": {"tags": ["用户管理"], "summary": "修改用户个人信息", "description": "修改当前登录用户的个人信息", "security": [{"Bearer": []}], "parameters": [{"name": "user", "in": "body", "description": "用户信息", "required": true, "schema": {"$ref": "#/definitions/UserProfileUpdate"}}], "responses": {"200": {"description": "成功", "schema": {"type": "object", "properties": {"msg": {"type": "string", "example": "操作成功"}, "code": {"type": "integer", "example": 200}}}}, "401": {"description": "未授权"}, "500": {"description": "服务器内部错误"}}}}, "/system/user/authRole/{userId}": {"get": {"tags": ["用户管理"], "summary": "查询授权角色", "description": "获取指定用户的授权角色信息", "security": [{"Bearer": []}], "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "成功", "schema": {"type": "object", "properties": {"msg": {"type": "string", "example": "操作成功"}, "code": {"type": "integer", "example": 200}, "user": {"$ref": "#/definitions/User"}, "roles": {"type": "array", "items": {"$ref": "#/definitions/Role"}}}}}, "401": {"description": "未授权"}, "500": {"description": "服务器内部错误"}}}}, "/system/user/importData": {"post": {"tags": ["用户管理"], "summary": "导入用户数据", "description": "批量导入用户数据", "security": [{"Bearer": []}], "consumes": ["multipart/form-data"], "parameters": [{"name": "file", "in": "formData", "description": "导入的Excel文件", "required": true, "type": "file"}, {"name": "updateSupport", "in": "query", "description": "是否更新已存在的用户数据", "required": false, "type": "integer", "default": 0}], "responses": {"200": {"description": "成功", "schema": {"type": "object", "properties": {"msg": {"type": "string", "example": "操作成功"}, "code": {"type": "integer", "example": 200}}}}, "401": {"description": "未授权"}, "500": {"description": "服务器内部错误"}}}}, "/system/dict/data/type/{dictType}": {"get": {"tags": ["字典"], "summary": "获取字典", "description": "根据字典类型获取字典数据", "produces": ["application/json"], "parameters": [{"name": "dictType", "in": "path", "description": "字典类型", "required": true, "type": "string"}], "responses": {"200": {"description": "操作成功", "schema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "example": 200, "description": "状态码"}, "msg": {"type": "string", "example": "操作成功", "description": "返回消息"}, "data": {"type": "array", "description": "字典数据列表", "items": {"type": "object", "properties": {"dictSort": {"type": "integer", "example": 1, "description": "字典排序"}, "dictLabel": {"type": "string", "example": "男", "description": "字典标签"}, "dictValue": {"type": "string", "example": "0", "description": "字典键值"}, "dictType": {"type": "string", "example": "sys_user_sex", "description": "字典类型"}, "cssClass": {"type": "string", "example": "", "description": "样式属性"}, "listClass": {"type": "string", "example": "", "description": "表格回显样式"}, "status": {"type": "string", "example": "0", "description": "状态"}}}}}}}}}}}, "definitions": {"DeptTreeNode": {"type": "object", "properties": {"id": {"type": "integer", "example": 100, "description": "部门ID"}, "label": {"type": "string", "example": "若依科技", "description": "部门名称"}, "children": {"type": "array", "description": "子部门", "items": {"$ref": "#/definitions/DeptTreeNode"}}}, "required": ["id", "label"]}, "User": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int64", "description": "用户ID"}, "deptId": {"type": "integer", "format": "int64", "description": "部门ID"}, "userName": {"type": "string", "description": "用户账号"}, "nickName": {"type": "string", "description": "用户昵称"}, "email": {"type": "string", "description": "用户邮箱"}, "phonenumber": {"type": "string", "description": "手机号码"}, "sex": {"type": "string", "description": "用户性别（0男 1女 2未知）"}, "avatar": {"type": "string", "description": "头像地址"}, "password": {"type": "string", "description": "密码"}, "status": {"type": "string", "description": "帐号状态（0正常 1停用）"}, "delFlag": {"type": "string", "description": "删除标志（0代表存在 2代表删除）"}, "loginIp": {"type": "string", "description": "最后登录IP"}, "loginDate": {"type": "string", "format": "date-time", "description": "最后登录时间"}, "createBy": {"type": "string", "description": "创建者"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateBy": {"type": "string", "description": "更新者"}, "updateTime": {"type": "string", "format": "date-time", "description": "更新时间"}, "remark": {"type": "string", "description": "备注"}, "dept": {"$ref": "#/definitions/Dept"}}}, "UserCreate": {"type": "object", "properties": {"userName": {"type": "string", "description": "用户账号"}, "nickName": {"type": "string", "description": "用户昵称"}, "password": {"type": "string", "description": "密码"}, "deptId": {"type": "integer", "format": "int64", "description": "部门ID"}, "email": {"type": "string", "description": "用户邮箱"}, "phonenumber": {"type": "string", "description": "手机号码"}, "sex": {"type": "string", "description": "用户性别（0男 1女 2未知）"}, "status": {"type": "string", "description": "帐号状态（0正常 1停用）"}, "postIds": {"type": "array", "description": "岗位ID列表", "items": {"type": "integer", "format": "int64"}}, "roleIds": {"type": "array", "description": "角色ID列表", "items": {"type": "integer", "format": "int64"}}, "remark": {"type": "string", "description": "备注"}}, "required": ["userName", "nick<PERSON><PERSON>", "password", "deptId"]}, "UserUpdate": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int64", "description": "用户ID"}, "userName": {"type": "string", "description": "用户账号"}, "nickName": {"type": "string", "description": "用户昵称"}, "deptId": {"type": "integer", "format": "int64", "description": "部门ID"}, "email": {"type": "string", "description": "用户邮箱"}, "phonenumber": {"type": "string", "description": "手机号码"}, "sex": {"type": "string", "description": "用户性别（0男 1女 2未知）"}, "status": {"type": "string", "description": "帐号状态（0正常 1停用）"}, "postIds": {"type": "array", "description": "岗位ID列表", "items": {"type": "integer", "format": "int64"}}, "roleIds": {"type": "array", "description": "角色ID列表", "items": {"type": "integer", "format": "int64"}}, "remark": {"type": "string", "description": "备注"}}, "required": ["userId", "userName", "nick<PERSON><PERSON>", "deptId"]}, "UserProfileUpdate": {"type": "object", "properties": {"userName": {"type": "string", "description": "用户账号"}, "nickName": {"type": "string", "description": "用户昵称"}, "email": {"type": "string", "description": "用户邮箱"}, "phonenumber": {"type": "string", "description": "手机号码"}, "sex": {"type": "string", "description": "用户性别（0男 1女 2未知）"}}, "required": ["nick<PERSON><PERSON>"]}, "Role": {"type": "object", "properties": {"roleId": {"type": "integer", "format": "int64", "description": "角色ID"}, "roleName": {"type": "string", "description": "角色名称"}, "roleKey": {"type": "string", "description": "角色权限字符串"}, "roleSort": {"type": "integer", "description": "显示顺序"}, "dataScope": {"type": "string", "description": "数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）"}, "menuCheckStrictly": {"type": "boolean", "description": "菜单树选择项是否关联显示"}, "deptCheckStrictly": {"type": "boolean", "description": "部门树选择项是否关联显示"}, "status": {"type": "string", "description": "角色状态（0正常 1停用）"}, "delFlag": {"type": "string", "description": "删除标志（0代表存在 2代表删除）"}, "createBy": {"type": "string", "description": "创建者"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateBy": {"type": "string", "description": "更新者"}, "updateTime": {"type": "string", "format": "date-time", "description": "更新时间"}, "remark": {"type": "string", "description": "备注"}}}, "Post": {"type": "object", "properties": {"postId": {"type": "integer", "format": "int64", "description": "岗位ID"}, "postCode": {"type": "string", "description": "岗位编码"}, "postName": {"type": "string", "description": "岗位名称"}, "postSort": {"type": "integer", "description": "显示顺序"}, "status": {"type": "string", "description": "状态（0正常 1停用）"}, "createBy": {"type": "string", "description": "创建者"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateBy": {"type": "string", "description": "更新者"}, "updateTime": {"type": "string", "format": "date-time", "description": "更新时间"}, "remark": {"type": "string", "description": "备注"}}}, "Dept": {"type": "object", "properties": {"deptId": {"type": "integer", "format": "int64", "description": "部门ID"}, "parentId": {"type": "integer", "format": "int64", "description": "父部门ID"}, "ancestors": {"type": "string", "description": "祖级列表"}, "deptName": {"type": "string", "description": "部门名称"}, "orderNum": {"type": "integer", "description": "显示顺序"}, "leader": {"type": "string", "description": "负责人"}, "phone": {"type": "string", "description": "联系电话"}, "email": {"type": "string", "description": "邮箱"}, "status": {"type": "string", "description": "部门状态（0正常 1停用）"}, "delFlag": {"type": "string", "description": "删除标志（0代表存在 2代表删除）"}, "createBy": {"type": "string", "description": "创建者"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateBy": {"type": "string", "description": "更新者"}, "updateTime": {"type": "string", "format": "date-time", "description": "更新时间"}}}}}