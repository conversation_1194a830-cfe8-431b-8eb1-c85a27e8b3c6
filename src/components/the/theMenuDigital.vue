<script setup lang="ts">
import { useRoute } from "vue-router";
const { collapse } = pinia.storeToRefs(storeApp());

const route = useRoute();
const options = dataRoutesDigital[0].children;

// 根据route.name获取options中name所在的数组项
const getActiveItem = (itemName: string) => {
  // 直接匹配
  if (route.name === itemName) {
    return true;
  }
  
  // 查找当前激活的菜单项（检查子路由）
  const currentItem = options?.find(item => item.name === itemName);
  if (currentItem && currentItem.children) {
    return currentItem.children.some(child => child.name === route.name);
  }
  
  return false;
};

// 点击跳转
function onItem(item: TsStore.RouteDynamic): void {
  item.children && item.children[0] && router.push({
    name: item.children[0].name
  })
}
</script>

<template>
  <div class="the-menu-digital">
    <div :class="{'menu-digital-item': true, 'collapse': collapse, 'active': getActiveItem(item.name)}" v-for="item in options"
         :key="item.name" @click="onItem(item)">
      <div class="menu-digital-icon">
        <base-icon :icon="item.meta.icon" v-if="item.meta.icon"></base-icon>
      </div>
      <div class="menu-digital-label">{{ item.meta.label }}</div>
    </div>
  </div>
</template>

<style scoped>
.the-menu-digital {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--el-gap-half);
  padding: var(--el-gap) 0;
  background-color: var(--el-menu-bg-color);
  height: 100%;
  overflow: auto;
}

.menu-digital-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--el-gap-half);
  width: fit-content;
  border-radius: var(--el-border-radius-round);
  cursor: pointer;
  color: var(--el-color-white);
  padding: var(--el-gap-half);
  transition: all 0.2s ease-in-out;
}

.menu-digital-item.active,
.menu-digital-item:hover {
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-7);
}

.menu-digital-item.collapse.active,
.menu-digital-item.collapse:hover {
  background-color: transparent;
}

.menu-digital-icon {
  transition: transform 0.3s ease;
  font-size: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: inherit;
}

.menu-digital-label {
  font-size: 14px;
  color: inherit;
}
</style>