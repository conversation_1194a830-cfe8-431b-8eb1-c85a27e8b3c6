<script setup lang="ts">
const options: TsDigital.AudioOption[] = [
    {
        id: "1",
        img: "https://picsum.photos/100/100?random=1",
        state: 1,
        title: "音频作品1",
        createTime: "2021-01-01",
    },
    {
        id: "2",
        img: "https://picsum.photos/100/100?random=2",
        state: 2,
        title: "音频作品2",
        createTime: "2021-02-01",
    },
    {
        id: "3",
        img: "https://picsum.photos/100/100?random=3",
        state: 3,
        title: "音频作品3",
        createTime: "2021-03-01",
    },
    {
        id: "4",
        img: "https://picsum.photos/100/100?random=4",
        state: 1,
        title: "音频作品4",
        createTime: "2021-04-01",
    },
];

// 表单数据
const formData = reactive({
    title: "",
    state: undefined,
});

// 获取状态字典数据
const statusOptions = ref([
    { label: "待审核", value: 1 },
    { label: "已发布", value: 2 },
    { label: "已下架", value: 3 },
]);

// 筛选后的数据
const filteredOptions = computed(() => {
    return options.filter((item) => {
        const titleMatch = !formData.title || item.title.includes(formData.title);
        const stateMatch = formData.state === undefined || item.state === formData.state;
        return titleMatch && stateMatch;
    });
});

// 重置筛选
function handleReset() {
    formData.title = "";
    formData.state = undefined;
}
</script>
<template>
    <base-form :model-value="formData" :sets="{ inline: true }">
        <base-form-item label="作品名称" prop="title">
            <base-input v-model="formData.title" placeholder="请输入作品名称" />
        </base-form-item>
        <base-form-item label="状态" prop="state">
            <base-select v-model="formData.state" :options="statusOptions" placeholder="请选择状态" clearable />
        </base-form-item>
        <base-form-item>
            <base-button @click="handleReset">重置</base-button>
        </base-form-item>
    </base-form>
    <div class="audio-container">
        <div class="audio-new">
            <base-icon icon="solar:add-square-bold-duotone" />
            <span>新增音频</span>
        </div>
        <digital-audio-part v-for="option in filteredOptions" :key="option.id" :option="option" />
    </div>
</template>
<style scoped lang="scss">
.audio-container {
    display: flex;
    gap: var(--el-gap);
    flex-wrap: wrap;
    padding: var(--el-gap);
    background-color: var(--el-bg-color);
    border-radius: var(--el-border-radius-round);
}
.audio-new {
  width: 350px;
    height: 140px;
    border-radius: var(--el-border-radius-round);
    background-color: var(--el-bg-color);
    box-shadow: var(--el-box-shadow-light);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--el-gap);
    cursor: pointer;
    span {
        color: var(--el-color-primary);
    }
    .base-icon {
        font-size: 42px;
        color: var(--el-color-primary-light-5);
        transition: all 0.3s ease;
    }
    &:hover .base-icon {
        color: var(--el-color-primary);
    }
}
</style>