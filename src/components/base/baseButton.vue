<script setup lang="ts">
const {sets = {}} = defineProps<{
    sets?: TsButton.Sets
}>()

// 是否在table组件中
const isTableIn = inject<boolean>("isTableIn",false);
// 按钮图标
const icon = ref("");
// 默认type
const type = ref<TsButton.Type>("primary");
// 按钮文字实例
const buttonTextEl = useTemplateRef<HTMLSpanElement>("buttonTextEl");
// 按钮文字
const buttonText = computed(() => buttonTextEl.value?.textContent);
// 若无按钮文字隐藏span
const isText = ref<boolean>(true);
// 获取插槽
const getSlots = useSlots();
// 按钮预设
function setInfoIcon() {
    switch (buttonText.value) {
        case "确定":
        case "提交":
            icon.value = "solar:unread-linear";
            break;
        case "保存":
            icon.value = "solar:folder-with-files-linear";
            break;
        case "取消":
            type.value = "warning";
            icon.value = "solar:close-circle-outline";
            break;
        case "重置":
        case "重置密码":
            icon.value = "solar:restart-bold";
            type.value = "warning";
            break;
        case "刷新":
            icon.value = "solar:refresh-bold";
            break;
        case "编辑":
        case "修改":
            icon.value = "solar:pen-2-bold-duotone";
            break;
        case "详情":
        case "查看":
            icon.value = "solar:eye-linear";
            break;
        case "退出":
        case "登出":
        case "注销":
            icon.value = "solar:power-linear";
            type.value = "danger";
            break;
        case "导入":
        case "上传":
            icon.value = "solar:cloud-upload-linear";
            type.value = "success";
            break;
        case "导出":
        case "下载":
            icon.value = "solar:cloud-download-linear";
            type.value = "success";
            break;
        case "新增":
        case "添加":
            icon.value = "solar:add-square-linear";
            break;
        case "删除":
        case "批量删除":
            icon.value = "solar:trash-bin-trash-linear";
            type.value = "danger";
            break;
        case "打印":
            icon.value = "solar:printer-2-linear";
            break;
        case "锁屏":
        case "密码":
            icon.value = "solar:lock-keyhole-minimalistic-linear";
            break;
        case "查询":
        case "搜索":
            icon.value = "solar:minimalistic-magnifer-linear";
            break;
        default:
            icon.value = "";
            break;
    }
}

onMounted(() => {
    isText.value = !!getSlots.default;
    setInfoIcon();
})
</script>
<template>
    <el-button
        :class="{'base-button': true,'block': sets.block,'no-text':!isText}"
        :type="type"
        :link="isTableIn"
        :round="true"
        v-bind="sets"
    >
        <template #icon v-if="icon">
            <base-icon :icon="icon"></base-icon>
        </template>
        <span ref="buttonTextEl"><slot></slot></span>
    </el-button>
</template>
<style scoped>
.base-button.block {
    width: 100%;
}
.base-button.no-text :deep(> span) {
    display: none;
}
</style>