namespace TsApis {
	// 提取请求体数据类型
	type ApiData<M extends keyof Apis, T extends keyof Apis[M]> = Parameters<(typeof Apis)[M][T]>[0] extends { data: infer D } ? D : never;

	// 提取请求参数类型
	type ApiParams<M extends keyof Apis, T extends keyof Apis[M]> = Parameters<(typeof Apis)[M][T]>[0] extends { params: infer P } ? P : never;
	
	// 提取不包含分页参数的请求参数类型
	type ApiParamsWithoutPage<M extends keyof Apis, T extends keyof Apis[M]> = Omit<ApiParams<M, T>, 'pageNum' | 'pageSize'>;

	// 提取路径参数类型
	type ApiPathParams<M extends keyof Apis, T extends keyof Apis[M]> = Parameters<(typeof Apis)[M][T]>[0] extends { pathParams: infer P } ? P : never;

	// 提取响应结果类型
	type ApiResponse<M extends keyof Apis, T extends keyof Apis[M]> = Awaited<ReturnType<ReturnType<(typeof Apis)[M][T]>["send"]>>;

}