export default {
  // 获取字典
  dict: (params: TsApis.ApiPathParams<"general", "get_system_dict_data_type_dicttype">) => {
    const method = Apis.general.get_system_dict_data_type_dicttype({
      name: "get_system_dict_data_type_dicttype",
      pathParams: params,
      transform: (res) => {
        return {
          options: (res.data || []).map(item => ({
            label: item.dictLabel || "",
            value: item.dictValue || "",
          })),
          response: res.data || []
        }
      }
    })
    return alova.useRequest(
      () => method,
      {
        immediate: true,
        middleware: alova.actionDelegationMiddleware("get_system_dict_data_type_dicttype"),
        initialData: {
          options: [],
          response: []
        }
      }
    );
  },
};
