namespace TsSelect {
    type ModelItem = string | number | boolean
    type Model = ModelItem | ModelItem[];

    interface Sets extends Partial<TsVue.ExtractPropTypes<TsElementPlus.SelectProps>> {
    }

    interface Option {
        label: string;
        value: Exclude<ModelItem, boolean>;
        disabled?: boolean;
    }

    type Options = Option[];
}
namespace TsTreeSelect {
    type Id = TsTree.Id;
    type Model = TsTree.Model;
    interface Option extends TsTree.Option {
    }
    type Options = Option[];
    interface Sets extends TsTree.Sets, TsSelect.Sets {}
}