<script setup lang="ts">
// 表单ref
const formRef = useTemplateRef<TsForm.El>("formRef");
// 表单数据
const formData = reactive<TsApis.ApiParamsWithoutPage<"general", "get_system_user_list">>({
	deptId: undefined,
	userName: "",
	phonenumber: "",
	status: undefined,
});
// 获取状态字典数据
const { data: statusOptions } = apiDict.dict({ dictType: "sys_normal_disable" });
// 获取用户列表数据
const { loading: loadingTable, data: userData, page: currentPage, pageSize, total, reload: reloadTable } = apiUser.getList(formData);
// 重置按钮
function handleReset() {
	formData.deptId = undefined;
	formRef.value?.resetFields();
	reloadTable();
}
// 删除用户
const userIdDelete = ref<TsUser.Id>(0);
const { send: deleteUser } = apiUser.delete(userIdDelete);
function handleDelete(row: (typeof userData.value)[number]) {
	if (!row.userId) return;
	ElMessageBox.confirm("确定要删除用户【"+row.nickName+"】吗？", "警告", {
		confirmButtonText: "确定",
		cancelButtonText: "取消",
		type: "warning",
	}).then(() => {
		userIdDelete.value = row.userId as TsUser.Id;
		deleteUser();
	});
}

// 用户新增弹框ref
const userAddRef = useTemplateRef<{ open: () => void }>("userAddRef");
// 打开新增用户弹框
function handleAdd() {
	userAddRef.value?.open();
}

// 用户编辑弹框ref
const userEditRef = useTemplateRef<{ open: (id: TsUser.Id) => void }>("userEditRef");
// 打开编辑用户弹框
function handleEdit(row: (typeof userData.value)[number]) {
	if (!row.userId) return;
	userEditRef.value?.open(row.userId as TsUser.Id);
}

// 用户重置密码弹框ref
const userResetPasswordRef = useTemplateRef<{ open: (row: { userId: TsUser.Id; userName: string; nickName: string }) => void }>("userResetPasswordRef");
// 打开重置密码弹框
function handleResetPassword(row: (typeof userData.value)[number]) {
	if (!row.userId) return;
	userResetPasswordRef.value?.open({
		userId: row.userId as TsUser.Id,
		userName: row.userName || "",
		nickName: row.nickName || ""
	});
}
</script>

<template>
	<layout-table>
		<!-- 左侧部门树 -->
		<template #left>
			<the-tree-dept v-model="formData.deptId" />
		</template>

		<!-- 操作按钮 -->
		<template #handle>
			<base-button @click="handleAdd">新增</base-button>
		</template>

		<!-- 查询表单 -->
		<template #form>
			<base-form :model-value="formData" :sets="{ inline: true }" ref="formRef" @keyup.enter="reloadTable">
				<base-form-item label="用户名称" prop="userName">
					<base-input v-model="formData.userName" placeholder="请输入用户名称" />
				</base-form-item>
				<base-form-item label="手机号码" prop="phonenumber">
					<base-input v-model="formData.phonenumber" placeholder="请输入手机号码" />
				</base-form-item>
				<base-form-item label="状态" prop="status">
					<base-select v-model="formData.status" :options="statusOptions.options" placeholder="用户状态" clearable />
				</base-form-item>
				<base-form-item>
					<base-button @click="reloadTable">查询</base-button>
					<base-button @click="handleReset">重置</base-button>
				</base-form-item>
			</base-form>
		</template>

		<!-- 表格 -->
		<template #table>
			<base-table v-model="userData" :sets="{ rowKey: 'userId' }" v-loading="loadingTable">
				<base-table-special type="selection" />
				<base-table-special type="index" :page="{ pageNum: currentPage, pageSize: pageSize }" />
				<base-table-column label="用户名称" prop="userName" />
				<base-table-column label="用户昵称" prop="nickName" />
				<base-table-column label="部门" prop="deptName" />
				<base-table-column label="手机号码" prop="phonenumber" />
				<base-table-dict label="状态" prop="status" :options="statusOptions.response" />
				<base-table-date label="创建时间" prop="createTime" />
				<base-table-special type="handle" width="240">
					<template #default="{ row }">
						<base-button @click="handleEdit(row)">编辑</base-button>
						<base-button @click="handleResetPassword(row)">重置密码</base-button>
						<base-button @click="handleDelete(row)">删除</base-button>
					</template>
				</base-table-special>
			</base-table>
		</template>

		<!-- 分页 -->
		<template #pagination>
			<base-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total" />
		</template>
	</layout-table>

	<!-- 用户新增弹框 -->
	<user-add ref="userAddRef" />
	
	<!-- 用户编辑弹框 -->
	<user-edit ref="userEditRef" />

	<!-- 用户重置密码弹框 -->
	<user-reset-password ref="userResetPasswordRef" />
</template>
<style scoped></style>
