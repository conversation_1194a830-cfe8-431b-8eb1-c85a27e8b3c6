namespace TsTree {
    type El = InstanceType<typeof BaseTree>;
    type Id = string | number;
    type Model = Id | Id[];

    interface Option {
        label: string;
        value: Id;
        children?: Options;
        isLeaf?: boolean;
        disabled?: boolean;
    }

    type Options = Option[];
    type Props = {
        [p in keyof Omit<TsElementTree.TreeOptionProps, "class">]: keyof Option;
    }

    interface Sets extends Partial<TsElementPlus.TreeInstance['$props']> {
    }
}